# LC-MSMS (DDA) 数据结构交互式教学工具

## 项目简介

本项目提供了一套完整的LC-MSMS（液相色谱-串联质谱）数据依赖采集（DDA）模式的交互式教学工具，旨在帮助学生和研究人员深入理解LC-MSMS数据的结构特征和采集原理。

## 文件说明

### 1. `lc_msms_interactive.html` - 基础版教学页面
- **功能**：基础的LC-MSMS数据可视化
- **特点**：
  - 3D MS1数据可视化（保留时间 × 质荷比 × 强度）
  - 2D MS2前体离子选择可视化
  - 色谱图对比分析
  - 实时参数调节（TopN值、动态排除时间等）
  - 仪器工作原理流程图

### 2. `advanced_lc_msms.html` - 高级版教学页面
- **功能**：基于真实代谢物数据的高级教学工具
- **特点**：
  - 多标签页界面设计
  - 真实代谢物数据库（咖啡因、葡萄糖、色氨酸等）
  - MS2碎片离子谱图展示
  - 数据统计分析
  - DDA vs DIA对比说明
  - 参数优化建议

### 3. `sample_data_generator.js` - 数据生成器
- **功能**：生成模拟的LC-MSMS数据
- **特点**：
  - 基于真实代谢物参数
  - 高斯分布色谱峰模拟
  - TopN和动态排除算法实现
  - MS2碎片离子生成
  - 可配置的噪声水平

## 核心教学概念

### MS1数据特征
- **三维数据结构**：保留时间(RT) × 质荷比(m/z) × 强度(Intensity)
- **相对"连续"**：无筛选行为，有什么扫什么，每个化合物在色谱维度呈现高斯分布
- **用途**：定量分析，化合物检测

### MS2数据特征
- **三维数据结构**：前体离子 × 质荷比(m/z) × 强度(Intensity)
- **"不连续"**：有筛选行为，受TopN策略和动态排除影响，扫描具有随机性
- **用途**：结构鉴定，化合物确认

### DDA工作原理
1. **MS1全扫描**：质谱仪对所有进入的离子进行全扫描，无筛选行为
2. **数据处理**：实时分析MS1数据，根据TopN策略选择强度最高的N个离子，并应用动态排除规则
3. **MS2碎裂扫描**：质谱仪切换到MS2模式，仅对选中的前体离子进行碎裂扫描
4. **循环切换**：质谱仪在MS1和MS2模式间快速切换，每个时刻只能执行一种扫描模式

### 关键概念理解
- **时间互斥性**：质谱仪在同一时刻只能进行MS1或MS2扫描，不存在绝对意义上的连续
- **MS1"连续"**：指无筛选行为，有什么扫什么，每次扫描都获取完整质谱信息
- **MS2"不连续"**：指有筛选行为，受TopN选择和动态排除影响，扫描具有随机性和不完整性

## 📊 数据可视化功能

### 1. MS1 三维数据可视化 + MS2前体离子选择
- **三维散点图**：保留时间 × 质荷比 × 强度
- **精选教学化合物**：6个氨基酸化合物，分为两组展示不同分离特征
  - 第一组：m/z相同，保留时间差异明显（苯丙氨酸、酪氨酸、色氨酸）
  - 第二组：保留时间相同，m/z差异明显（甘氨酸、亮氨酸、精氨酸）
- **优化扫描点数量**：每个化合物40-60个扫描点，严格遵循高斯分布
- **MS2选择可视化**：红色点表示被TopN选择的前体离子，灰色点表示未被选择
- **交互式操作**：旋转、缩放、悬停查看详情
- **动态排除效果**：直观展示DDA选择性扫描的三维分布

### 2. MS2 碎片离子谱图
- **线条式谱图**：质荷比 × 相对强度，使用垂直线条显示碎片离子
- **碎片离子类型**：H₂O丢失、NH₃丢失、CO₂丢失等典型碎片
- **结构鉴定辅助**：展示碰撞诱导解离(CID)的碎裂模式
- **真实模拟**：基于代谢物分子结构的碎片离子生成

## 使用方法

### 基础版使用
1. 打开 `lc_msms_interactive.html`
2. 调整控制面板中的参数：
   - TopN值：控制每次扫描选择的前体离子数量（1-3）
   - 动态排除时间：滑块控制前体离子被排除的时间长度（0-60秒）
   - 保留时间范围：设置分析的时间窗口
   - 化合物数量：控制显示的化合物数量（3-6种）
3. 观察MS1三维图表中红色和灰色点的分布变化
4. 查看MS2碎片离子谱图了解碎裂模式

### 高级版使用
1. 打开 `advanced_lc_msms.html`
2. 浏览不同标签页：
   - **理论基础**：了解DDA原理和代谢物信息
   - **数据可视化**：交互式调节参数观察数据变化
   - **对比研究**：学习DDA vs DIA的区别
3. 在数据可视化页面调节参数观察MS1+MS2集成可视化效果
4. 查看MS2碎片离子谱图了解碎裂模式

## 教学要点

### 1. 数据扫描特性对比
- **MS1数据**：相对"连续"（无筛选行为）、完整、符合高斯分布
- **MS2数据**："不连续"（有筛选行为）、随机性、受TopN和动态排除算法控制

### 2. 参数影响分析
- **TopN值增加**：提高化合物覆盖率，但降低扫描频率
- **动态排除时间延长**：减少重复扫描，但可能错过色谱峰尾部
- **质量窗口大小**：影响选择性和灵敏度

### 3. 实际应用考虑
- **样品复杂度**：复杂样品需要更高的TopN值
- **色谱峰宽度**：影响动态排除时间设置
- **分析目标**：定量vs定性分析的参数优化策略

## 技术实现

### 前端技术
- **HTML5 + CSS3**：响应式界面设计
- **JavaScript ES6**：数据处理和交互逻辑
- **Plotly.js**：3D和2D数据可视化

### 数据模拟算法
- **高斯分布生成**：Box-Muller变换
- **色谱峰模拟**：二维高斯函数
- **TopN算法**：强度排序和动态排除
- **碎片离子生成**：基于质量损失的随机模型

## 扩展功能建议

1. **添加更多代谢物**：扩展代谢物数据库
2. **实际数据导入**：支持mzML等标准格式
3. **参数优化向导**：自动推荐最佳参数组合
4. **性能评估指标**：覆盖率、重现性等量化指标
5. **多种碎裂模式**：HCD、ETD等不同碎裂方式

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 联系信息

如有问题或建议，请联系开发团队。

---

*本教学工具仅用于教育目的，模拟数据不代表真实实验结果。*
