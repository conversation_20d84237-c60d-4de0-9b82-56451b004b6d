// LC-MSMS 数据生成器演示脚本
// 展示如何使用LCMSMSDataGenerator类生成教学数据

// 检查是否在Node.js环境中运行
const isNode = typeof module !== 'undefined' && module.exports;

if (isNode) {
    // Node.js环境
    const LCMSMSDataGenerator = require('./sample_data_generator.js');
    const fs = require('fs');
    
    console.log('=== LC-MSMS 数据生成器演示 ===\n');
    
    // 创建数据生成器实例
    const generator = new LCMSMSDataGenerator();
    
    // 演示1：基本数据生成
    console.log('1. 基本数据生成演示');
    console.log('参数：RT范围=20min, TopN=10, 动态排除=20s');
    
    const basicData = generator.exportData(20, 10, 20);
    console.log(`生成的MS1数据点：${basicData.ms1_data.length}`);
    console.log(`生成的MS2数据点：${basicData.ms2_data.length}`);
    console.log(`包含的代谢物：${basicData.metabolites.length}\n`);
    
    // 演示2：参数对比实验
    console.log('2. 参数对比实验');
    const parameterSets = [
        { topN: 5, exclusionTime: 10, label: 'Top5, 10s排除' },
        { topN: 10, exclusionTime: 20, label: 'Top10, 20s排除' },
        { topN: 20, exclusionTime: 30, label: 'Top20, 30s排除' }
    ];
    
    parameterSets.forEach(params => {
        const data = generator.exportData(20, params.topN, params.exclusionTime);
        const uniquePrecursors = new Set(data.ms2_data.map(d => d.precursor_mz)).size;
        const coverage = (uniquePrecursors / data.metabolites.length * 100).toFixed(1);
        
        console.log(`${params.label}:`);
        console.log(`  MS2数据点: ${data.ms2_data.length}`);
        console.log(`  唯一前体离子: ${uniquePrecursors}`);
        console.log(`  代谢物覆盖率: ${coverage}%`);
    });
    
    // 演示3：数据导出
    console.log('\n3. 数据导出演示');
    const exportData = generator.exportData(25, 15, 25);
    
    // 导出为JSON文件
    fs.writeFileSync('lc_msms_demo_data.json', JSON.stringify(exportData, null, 2));
    console.log('数据已导出到: lc_msms_demo_data.json');
    
    // 导出MS1数据为CSV格式
    const ms1CSV = 'RT,MZ,Intensity,CompoundID,CompoundName\n' +
        exportData.ms1_data.map(d => 
            `${d.rt},${d.mz},${d.intensity},${d.compound_id},${d.compound_name}`
        ).join('\n');
    
    fs.writeFileSync('ms1_data.csv', ms1CSV);
    console.log('MS1数据已导出到: ms1_data.csv');
    
    // 导出MS2数据为CSV格式
    const ms2CSV = 'RT,PrecursorMZ,FragmentMZ,Intensity,CompoundID,CompoundName\n' +
        exportData.ms2_data.map(d => 
            `${d.rt},${d.precursor_mz},${d.fragment_mz},${d.intensity},${d.compound_id},${d.compound_name}`
        ).join('\n');
    
    fs.writeFileSync('ms2_data.csv', ms2CSV);
    console.log('MS2数据已导出到: ms2_data.csv');
    
    // 演示4：代谢物信息统计
    console.log('\n4. 代谢物信息统计');
    console.log('代谢物列表:');
    exportData.metabolites.forEach((metabolite, index) => {
        const ms1Count = exportData.ms1_data.filter(d => d.compound_id === index).length;
        const ms2Count = exportData.ms2_data.filter(d => d.compound_id === index).length;
        
        console.log(`  ${metabolite.name}:`);
        console.log(`    m/z: ${metabolite.mz.toFixed(4)}`);
        console.log(`    RT: ${metabolite.rt.toFixed(1)} min`);
        console.log(`    MS1数据点: ${ms1Count}`);
        console.log(`    MS2数据点: ${ms2Count}`);
    });
    
    console.log('\n=== 演示完成 ===');
    
} else {
    // 浏览器环境演示
    console.log('=== 浏览器环境LC-MSMS演示 ===');
    
    // 等待页面加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 创建演示按钮
        const demoButton = document.createElement('button');
        demoButton.textContent = '运行数据生成演示';
        demoButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        `;
        
        demoButton.onclick = function() {
            runBrowserDemo();
        };
        
        document.body.appendChild(demoButton);
    });
    
    function runBrowserDemo() {
        console.log('开始浏览器演示...');
        
        if (typeof LCMSMSDataGenerator === 'undefined') {
            console.error('LCMSMSDataGenerator未加载，请确保sample_data_generator.js已正确引入');
            return;
        }
        
        const generator = new LCMSMSDataGenerator();
        
        // 生成不同参数的数据集
        const testCases = [
            { rtRange: 15, topN: 5, exclusionTime: 15 },
            { rtRange: 20, topN: 10, exclusionTime: 20 },
            { rtRange: 25, topN: 15, exclusionTime: 25 }
        ];
        
        testCases.forEach((params, index) => {
            console.log(`\n测试案例 ${index + 1}:`);
            console.log(`参数: RT=${params.rtRange}min, TopN=${params.topN}, 排除=${params.exclusionTime}s`);
            
            const data = generator.exportData(params.rtRange, params.topN, params.exclusionTime);
            
            console.log(`MS1数据点: ${data.ms1_data.length}`);
            console.log(`MS2数据点: ${data.ms2_data.length}`);
            
            const uniquePrecursors = new Set(data.ms2_data.map(d => d.precursor_mz)).size;
            const coverage = (uniquePrecursors / data.metabolites.length * 100).toFixed(1);
            console.log(`代谢物覆盖率: ${coverage}%`);
            
            // 显示前5个MS1数据点
            console.log('前5个MS1数据点:');
            data.ms1_data.slice(0, 5).forEach(d => {
                console.log(`  RT:${d.rt}, m/z:${d.mz.toFixed(4)}, 强度:${d.intensity.toFixed(0)}, 化合物:${d.compound_name}`);
            });
            
            // 显示前5个MS2数据点
            console.log('前5个MS2数据点:');
            data.ms2_data.slice(0, 5).forEach(d => {
                console.log(`  RT:${d.rt}, 前体:${d.precursor_mz.toFixed(4)}, 碎片:${d.fragment_mz.toFixed(4)}, 化合物:${d.compound_name}`);
            });
        });
        
        console.log('\n浏览器演示完成！');
        alert('演示完成！请查看浏览器控制台获取详细信息。');
    }
}

// 导出函数（如果在Node.js环境中）
if (isNode) {
    module.exports = {
        runDemo: function() {
            console.log('请直接运行此脚本文件进行演示');
        }
    };
}

// 使用说明
/*
Node.js环境使用方法:
1. 确保已安装Node.js
2. 在命令行中运行: node demo_script.js
3. 查看生成的CSV和JSON文件

浏览器环境使用方法:
1. 在HTML页面中引入sample_data_generator.js和demo_script.js
2. 打开浏览器开发者工具的控制台
3. 点击页面右上角的"运行数据生成演示"按钮
4. 查看控制台输出的演示结果
*/
