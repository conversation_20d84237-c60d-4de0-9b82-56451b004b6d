// LC-MSMS 样本数据生成器
// 用于生成更真实的LC-MSMS数据用于教学演示

class LCMSMSDataGenerator {
    constructor() {
        // 精选6个化合物用于教学演示
        this.commonMetabolites = [
            // 第一组：m/z相同，保留时间差异明显
            { name: "苯丙氨酸", mz: 165.0790, rt: 3.0, intensity_base: 8000, pathway: "氨基酸代谢", group: "A" },
            { name: "酪氨酸", mz: 165.0790, rt: 8.0, intensity_base: 7000, pathway: "氨基酸代谢", group: "A" },
            { name: "色氨酸", mz: 165.0790, rt: 13.0, intensity_base: 6000, pathway: "氨基酸代谢", group: "A" },

            // 第二组：保留时间相同，m/z差异明显
            { name: "甘氨酸", mz: 75.0320, rt: 6.0, intensity_base: 5000, pathway: "氨基酸代谢", group: "B" },
            { name: "亮氨酸", mz: 131.0946, rt: 6.0, intensity_base: 6500, pathway: "氨基酸代谢", group: "B" },
            { name: "精氨酸", mz: 174.1117, rt: 6.0, intensity_base: 5500, pathway: "氨基酸代谢", group: "B" }
        ];
    }

    // 生成高斯分布随机数
    gaussianRandom(mean = 0, stdev = 1) {
        let u = 0, v = 0;
        while(u === 0) u = Math.random();
        while(v === 0) v = Math.random();
        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v) * stdev + mean;
    }

    // 生成MS1数据
    generateMS1Data(rtRange = 30, noiseLevel = 0.1) {
        const ms1Data = [];
        
        this.commonMetabolites.forEach((metabolite, compoundId) => {
            if (metabolite.rt > rtRange) return;
            
            // 添加一些随机变化
            const rt_center = metabolite.rt + this.gaussianRandom(0, 0.2);
            const mz_center = metabolite.mz + this.gaussianRandom(0, 0.001);
            const intensity_max = metabolite.intensity_base * (0.8 + Math.random() * 0.4);
            
            // 色谱峰参数
            const rt_width = 0.3 + Math.random() * 0.4; // 色谱峰宽度
            const mz_width = 0.005 + Math.random() * 0.005; // 质量精度
            
            // 为每个化合物生成更多扫描点，严格按照高斯分布
            const numPoints = 40 + Math.floor(Math.random() * 21); // 40-60个点

            for (let p = 0; p < numPoints; p++) {
                // 使用Box-Muller变换生成高斯分布的随机数
                const rt_offset = this.gaussianRandom(0, rt_width);
                const mz_offset = this.gaussianRandom(0, mz_width * 0.5);

                const rt = rt_center + rt_offset;
                const mz = mz_center + mz_offset;

                if (rt >= 0 && rt <= rtRange && Math.abs(rt_offset) <= 3 * rt_width) {
                    // 计算高斯分布的强度
                    const rt_factor = Math.exp(-0.5 * Math.pow(rt_offset / rt_width, 2));
                    const mz_factor = Math.exp(-0.5 * Math.pow(mz_offset / (mz_width * 0.5), 2));
                    let intensity = intensity_max * rt_factor * mz_factor;

                    // 添加适量噪声
                    intensity += this.gaussianRandom(0, intensity * noiseLevel);
                    intensity = Math.max(0, intensity);

                    if (intensity > 100) { // 提高信噪比阈值
                        ms1Data.push({
                            rt: parseFloat(rt.toFixed(2)),
                            mz: parseFloat(mz.toFixed(4)),
                            intensity: parseFloat(intensity.toFixed(1)),
                            compound_id: compoundId,
                            compound_name: metabolite.name
                        });
                    }
                }
            }
        });
        
        // 添加背景噪声
        for (let i = 0; i < 1000; i++) {
            ms1Data.push({
                rt: Math.random() * rtRange,
                mz: 100 + Math.random() * 900,
                intensity: Math.random() * 200,
                compound_id: -1,
                compound_name: "Background"
            });
        }
        
        return ms1Data.sort((a, b) => a.rt - b.rt);
    }

    // 生成MS2数据（基于DDA策略）
    generateMS2Data(ms1Data, topN = 10, exclusionTime = 20, rtRange = 30) {
        const ms2Data = [];
        const excludedPrecursors = new Map();
        
        // 按时间步长进行DDA扫描
        for (let rt = 0; rt <= rtRange; rt += 0.1) {
            // 获取当前时间窗口的MS1数据
            const currentMS1 = ms1Data.filter(d => 
                Math.abs(d.rt - rt) < 0.05 && d.compound_id >= 0 // 排除背景噪声
            );
            
            if (currentMS1.length === 0) continue;
            
            // 按强度排序
            currentMS1.sort((a, b) => b.intensity - a.intensity);
            
            // 清理过期的排除列表
            for (let [precursor, excludeUntil] of excludedPrecursors) {
                if (rt > excludeUntil) {
                    excludedPrecursors.delete(precursor);
                }
            }
            
            // 选择TopN前体离子
            let selectedCount = 0;
            for (let point of currentMS1) {
                if (selectedCount >= topN) break;
                
                const precursorKey = Math.round(point.mz * 100) / 100;
                
                if (!excludedPrecursors.has(precursorKey) && point.intensity > 500) {
                    // 生成MS2碎片离子
                    const fragments = this.generateFragments(point.mz, point.intensity);
                    
                    fragments.forEach(fragment => {
                        ms2Data.push({
                            rt: parseFloat(rt.toFixed(2)),
                            precursor_mz: parseFloat(point.mz.toFixed(4)),
                            fragment_mz: parseFloat(fragment.mz.toFixed(4)),
                            intensity: parseFloat(fragment.intensity.toFixed(1)),
                            compound_id: point.compound_id,
                            compound_name: point.compound_name
                        });
                    });
                    
                    // 添加到排除列表
                    excludedPrecursors.set(precursorKey, rt + exclusionTime);
                    selectedCount++;
                }
            }
        }
        
        return ms2Data;
    }

    // 生成碎片离子（简化模拟）
    generateFragments(precursorMz, precursorIntensity) {
        const fragments = [];
        const numFragments = 3 + Math.floor(Math.random() * 8); // 3-10个碎片
        
        for (let i = 0; i < numFragments; i++) {
            const lossMass = 10 + Math.random() * (precursorMz * 0.6); // 丢失质量
            const fragmentMz = precursorMz - lossMass;
            const fragmentIntensity = precursorIntensity * (0.1 + Math.random() * 0.8);
            
            if (fragmentMz > 50) { // 最小碎片质量
                fragments.push({
                    mz: fragmentMz,
                    intensity: fragmentIntensity
                });
            }
        }
        
        return fragments.sort((a, b) => b.intensity - a.intensity);
    }

    // 导出数据为JSON格式
    exportData(rtRange = 30, topN = 10, exclusionTime = 20) {
        const ms1Data = this.generateMS1Data(rtRange);
        const ms2Data = this.generateMS2Data(ms1Data, topN, exclusionTime, rtRange);
        
        return {
            parameters: {
                rtRange: rtRange,
                topN: topN,
                exclusionTime: exclusionTime,
                compounds: this.commonMetabolites.length
            },
            ms1_data: ms1Data,
            ms2_data: ms2Data,
            metabolites: this.commonMetabolites
        };
    }
}

// 使用示例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LCMSMSDataGenerator;
} else {
    // 浏览器环境
    window.LCMSMSDataGenerator = LCMSMSDataGenerator;
}
