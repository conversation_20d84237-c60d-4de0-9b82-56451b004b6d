<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LC-MSMS (DDA) 数据结构交互式教学</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .plot-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .control-group {
            display: inline-block;
            margin: 10px 20px 10px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .control-group input, .control-group select {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .control-group input:focus, .control-group select:focus {
            border-color: #3498db;
            outline: none;
        }
        
        .info-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .info-box h3 {
            margin-top: 0;
            color: white;
        }
        
        .instrument-diagram {
            text-align: center;
            margin: 20px 0;
        }
        
        .flow-diagram {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .flow-step {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            margin: 10px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateY(-3px);
        }
        
        .arrow {
            font-size: 2em;
            color: #3498db;
            margin: 0 10px;
        }
        
        .data-characteristics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .characteristic-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        
        .characteristic-box h4 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        @media (max-width: 768px) {
            .data-characteristics {
                grid-template-columns: 1fr;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LC-MSMS (DDA) 数据结构交互式教学</h1>
            <p>液相色谱-串联质谱数据依赖采集模式的三维数据可视化</p>
        </div>
        
        <div class="content">
            <!-- 仪器原理部分 -->
            <div class="section">
                <h2>🔬 仪器模型与采集原理</h2>
                
                <div class="flow-diagram">
                    <div class="flow-step">样品注入</div>
                    <div class="arrow">→</div>
                    <div class="flow-step">液相色谱分离</div>
                    <div class="arrow">→</div>
                    <div class="flow-step">电离源</div>
                    <div class="arrow">→</div>
                    <div class="flow-step" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">质谱仪</div>
                </div>

                <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 5px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔄 DDA扫描循环模式</h4>
                    <div style="display: flex; justify-content: space-around; align-items: center; flex-wrap: wrap;">
                        <div style="text-align: center; margin: 10px;">
                            <div style="background: #3498db; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">MS1 全扫描</div>
                            <div style="font-size: 0.9em; color: #7f8c8d;">无筛选，获取所有离子</div>
                        </div>
                        <div style="font-size: 2em; color: #3498db;">↓</div>
                        <div style="text-align: center; margin: 10px;">
                            <div style="background: #e67e22; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">数据处理</div>
                            <div style="font-size: 0.9em; color: #7f8c8d;">TopN选择 + 动态排除</div>
                        </div>
                        <div style="font-size: 2em; color: #3498db;">↓</div>
                        <div style="text-align: center; margin: 10px;">
                            <div style="background: #e74c3c; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">MS2 碎裂扫描</div>
                            <div style="font-size: 0.9em; color: #7f8c8d;">有筛选，仅扫描选中离子</div>
                        </div>
                        <div style="font-size: 2em; color: #3498db;">↺</div>
                    </div>
                    <div style="text-align: center; margin-top: 15px; font-style: italic; color: #7f8c8d;">
                        质谱仪在MS1和MS2模式间快速切换，每个时刻只能执行一种扫描
                    </div>
                </div>
                
                <div class="info-box">
                    <h3>DDA (Data-Dependent Acquisition) 工作原理</h3>
                    <p><strong>MS1扫描：</strong>质谱仪对所有进入的离子进行全扫描，无筛选行为，获得完整的质谱图</p>
                    <p><strong>数据处理：</strong>实时分析MS1数据，根据TopN策略选择强度最高的N个离子，并应用动态排除规则</p>
                    <p><strong>MS2扫描：</strong>质谱仪切换到MS2模式，仅对选中的前体离子进行碎裂扫描</p>
                    <p><strong>循环模式：</strong>质谱仪在MS1和MS2模式间快速切换，每个时刻只能执行一种扫描模式</p>
                    <p><strong>扫描特性：</strong>MS1相对"连续"（无筛选），MS2"不连续"（有筛选+动态排除）</p>
                </div>
                
                <div class="data-characteristics">
                    <div class="characteristic-box">
                        <h4>MS1数据特征</h4>
                        <ul>
                            <li>三维数据：保留时间(RT) × 质荷比(m/z) × 强度</li>
                            <li>相对"连续"：无筛选行为，有什么扫什么</li>
                            <li>色谱峰呈高斯分布，数据完整</li>
                            <li>用于定量分析和化合物检测</li>
                        </ul>
                    </div>
                    <div class="characteristic-box">
                        <h4>MS2数据特征</h4>
                        <ul>
                            <li>三维数据：前体离子 × 质荷比(m/z) × 强度</li>
                            <li>"不连续"：有筛选行为，受TopN和动态排除影响</li>
                            <li>扫描随机性强，强度分布不规律</li>
                            <li>用于结构鉴定和化合物确认</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="section">
                <h2>🎛️ 数据参数控制</h2>
                <div class="controls">
                    <div class="control-group">
                        <label for="topN">TopN值:</label>
                        <select id="topN" onchange="updateData()">
                            <option value="1">Top 1</option>
                            <option value="2" selected>Top 2</option>
                            <option value="3">Top 3</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="exclusionTime">动态排除时间:</label>
                        <input type="range" id="exclusionTime" min="0" max="60" value="10" onchange="updateData()">
                        <span id="exclusionValue">10</span> 秒
                    </div>
                    
                    <div class="control-group">
                        <label for="rtRange">保留时间范围(min):</label>
                        <input type="range" id="rtRange" min="0" max="30" value="30" onchange="updateData()">
                        <span id="rtValue">30</span>
                    </div>
                    
                    <div class="control-group">
                        <label for="compounds">化合物数量:</label>
                        <input type="range" id="compounds" min="3" max="6" value="6" onchange="updateData()">
                        <span id="compoundsValue">6</span>
                    </div>
                </div>
            </div>

            <!-- MS1 3D数据可视化 -->
            <div class="section">
                <h2>📊 MS1 三维数据可视化</h2>
                <div class="info-box">
                    <h3>MS1数据 + MS2前体离子选择可视化</h3>
                    <p>X轴：保留时间(RT, min) | Y轴：质荷比(m/z) | Z轴：强度(Intensity)</p>
                    <p>MS1扫描无筛选行为，每次扫描获取完整质谱信息，因此相对"连续"</p>
                    <p><strong>🔴 红色点：</strong>被TopN策略选择进行MS2扫描的前体离子</p>
                    <p><strong>⚪ 灰色点：</strong>未被选择的离子（被TopN策略或动态排除过滤）</p>
                    <p>直观展示DDA选择性扫描在三维空间中的分布效果</p>
                </div>
                <div class="plot-container">
                    <div id="ms1Plot" style="width:100%;height:600px;"></div>
                </div>
            </div>

            <!-- MS2碎片离子谱图 -->
            <div class="section">
                <h2>🧪 MS2 碎片离子谱图</h2>
                <div class="info-box">
                    <h3>MS2碎片谱图说明</h3>
                    <p>X轴：质荷比(m/z) | Y轴：相对强度(%)</p>
                    <p>展示选中前体离子经过碰撞诱导解离(CID)后产生的碎片离子</p>
                    <p>不同的碎片离子模式有助于化合物结构鉴定</p>
                    <p><strong>常见碎片类型：</strong>H₂O丢失(-18)、NH₃丢失(-17)、CO₂丢失(-44)等</p>
                </div>
                <div class="plot-container">
                    <div id="ms2Plot" style="width:100%;height:400px;"></div>
                </div>
            </div>

            <!-- 色谱图对比 -->
            <div class="section">
                <h2>🔍 色谱图对比分析</h2>
                <div class="plot-container">
                    <div id="chromatogramPlot" style="width:100%;height:400px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let ms1Data = [];
        let ms2Data = [];
        let currentParams = {
            topN: 2,
            exclusionTime: 10,
            rtRange: 15,
            compounds: 6
        };

        // 生成高斯分布随机数
        function gaussianRandom(mean = 0, stdev = 1) {
            let u = 0, v = 0;
            while(u === 0) u = Math.random();
            while(v === 0) v = Math.random();
            return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v) * stdev + mean;
        }

        // 精选6个化合物用于教学演示
        const keggMetabolites = [
            // 第一组：m/z相同，保留时间差异明显
            { name: "苯丙氨酸", mz: 165.0790, rt: 3.0, pathway: "氨基酸代谢", group: "A" },
            { name: "酪氨酸", mz: 165.0790, rt: 8.0, pathway: "氨基酸代谢", group: "A" },
            { name: "色氨酸", mz: 165.0790, rt: 13.0, pathway: "氨基酸代谢", group: "A" },

            // 第二组：保留时间相同，m/z差异明显
            { name: "甘氨酸", mz: 75.0320, rt: 6.0, pathway: "氨基酸代谢", group: "B" },
            { name: "亮氨酸", mz: 131.0946, rt: 6.0, pathway: "氨基酸代谢", group: "B" },
            { name: "精氨酸", mz: 174.1117, rt: 6.0, pathway: "氨基酸代谢", group: "B" }
        ];

        // 生成MS1数据
        function generateMS1Data() {
            ms1Data = [];
            const compounds = Math.min(currentParams.compounds, keggMetabolites.length);
            const rtRange = currentParams.rtRange;

            for (let i = 0; i < compounds; i++) {
                const metabolite = keggMetabolites[i];
                const compound = {
                    id: i,
                    name: metabolite.name,
                    rt_center: (metabolite.rt / 15) * rtRange, // 标准化到rtRange
                    mz_center: metabolite.mz,
                    intensity_max: 1000 + Math.random() * 9000,
                    rt_width: 0.3 + Math.random() * 0.4, // 减小峰宽
                    mz_width: 0.005 + Math.random() * 0.01 // 减小质量窗口
                };

                // 为每个化合物生成更多扫描点，严格按照高斯分布
                const numPoints = 40 + Math.floor(Math.random() * 21); // 40-60个点

                for (let p = 0; p < numPoints; p++) {
                    // 使用Box-Muller变换生成高斯分布的随机数
                    const rt_offset = gaussianRandom(0, compound.rt_width);
                    const mz_offset = gaussianRandom(0, compound.mz_width * 0.5);

                    const rt = compound.rt_center + rt_offset;
                    const mz = compound.mz_center + mz_offset;

                    if (rt >= 0 && rt <= rtRange && Math.abs(rt_offset) <= 3 * compound.rt_width) {
                        // 计算高斯分布的强度
                        const rt_factor = Math.exp(-0.5 * Math.pow(rt_offset / compound.rt_width, 2));
                        const mz_factor = Math.exp(-0.5 * Math.pow(mz_offset / (compound.mz_width * 0.5), 2));
                        let intensity = compound.intensity_max * rt_factor * mz_factor;

                        // 添加适量噪声
                        intensity += gaussianRandom(0, intensity * 0.1);
                        intensity = Math.max(0, intensity);

                        if (intensity > 200) { // 提高信噪比阈值
                            ms1Data.push({
                                rt: rt,
                                mz: mz,
                                intensity: intensity,
                                compound_id: i,
                                compound_name: compound.name
                            });
                        }
                    }
                }
            }
        }

        // 生成MS2数据（基于TopN和动态排除）
        function generateMS2Data() {
            ms2Data = [];
            const topN = currentParams.topN;
            const exclusionTime = currentParams.exclusionTime;
            const rtRange = currentParams.rtRange;

            let excludedPrecursors = new Map(); // 存储被排除的前体离子及其排除时间

            // 按时间步长扫描
            for (let rt = 0; rt <= rtRange; rt += 0.2) {
                // 获取当前时间点的MS1数据
                const currentMS1 = ms1Data.filter(d => Math.abs(d.rt - rt) < 0.1);

                // 按强度排序
                currentMS1.sort((a, b) => b.intensity - a.intensity);

                // 清理过期的排除列表
                for (let [precursor, excludeUntil] of excludedPrecursors) {
                    if (rt > excludeUntil) {
                        excludedPrecursors.delete(precursor);
                    }
                }

                // 选择TopN前体离子（排除已被排除的）
                let selectedCount = 0;
                for (let point of currentMS1) {
                    if (selectedCount >= topN) break;

                    const precursorKey = Math.round(point.mz * 100) / 100; // 精确到0.01

                    if (!excludedPrecursors.has(precursorKey) && point.intensity > 1000) {
                        ms2Data.push({
                            rt: rt,
                            precursor_mz: point.mz,
                            intensity: point.intensity,
                            compound_id: point.compound_id
                        });

                        // 添加到排除列表
                        excludedPrecursors.set(precursorKey, rt + exclusionTime);
                        selectedCount++;
                    }
                }
            }
        }

        // 模拟MS2前体离子选择逻辑
        function simulateMS2Selection() {
            const scannedPrecursors = new Set();
            const excludedPrecursors = new Map(); // 存储排除的离子和排除结束时间

            // 按保留时间排序所有MS1数据点
            const sortedMS1Data = ms1Data.filter(d => d.compound_id >= 0)
                .sort((a, b) => a.rt - b.rt);

            // 按时间窗口模拟扫描过程
            const timeWindow = 0.1; // 0.1分钟时间窗口
            let currentTime = Math.min(...sortedMS1Data.map(d => d.rt));
            const maxTime = Math.max(...sortedMS1Data.map(d => d.rt));

            while (currentTime <= maxTime) {
                // 获取当前时间窗口内的所有离子
                const currentIons = sortedMS1Data.filter(d =>
                    d.rt >= currentTime && d.rt < currentTime + timeWindow
                );

                if (currentIons.length > 0) {
                    // 移除已过期的动态排除
                    for (let [key, excludeEndTime] of excludedPrecursors.entries()) {
                        if (currentTime >= excludeEndTime) {
                            excludedPrecursors.delete(key);
                        }
                    }

                    // 过滤掉被动态排除的离子
                    const availableIons = currentIons.filter(d => {
                        const key = `${d.mz.toFixed(4)}`;
                        return !excludedPrecursors.has(key);
                    });

                    // 按强度排序，选择TopN
                    const selectedIons = availableIons
                        .sort((a, b) => b.intensity - a.intensity)
                        .slice(0, topN);

                    // 记录被选择的离子
                    selectedIons.forEach(d => {
                        const key = `${d.rt.toFixed(1)}_${d.mz.toFixed(4)}`;
                        scannedPrecursors.add(key);

                        // 添加到动态排除列表
                        const excludeKey = `${d.mz.toFixed(4)}`;
                        const excludeEndTime = currentTime + exclusionTime / 60; // 转换为分钟
                        excludedPrecursors.set(excludeKey, excludeEndTime);
                    });
                }

                currentTime += timeWindow;
            }

            return scannedPrecursors;
        }

        // 绘制MS1 3D图（集成MS2前体离子选择可视化）
        function plotMS1Data() {
            // 实现MS2前体离子选择逻辑
            const scannedPrecursors = simulateMS2Selection();

            // 分离被扫描和未被扫描的数据点
            const scannedPoints = [];
            const unscannedPoints = [];

            ms1Data.forEach(d => {
                if (d.compound_id < 0) return; // 跳过噪声点

                const key = `${d.rt.toFixed(1)}_${d.mz.toFixed(4)}`;
                if (scannedPrecursors.has(key)) {
                    scannedPoints.push(d);
                } else {
                    unscannedPoints.push(d);
                }
            });

            // 未被扫描的离子（灰色）
            const unscannedTrace = {
                x: unscannedPoints.map(d => d.rt),
                y: unscannedPoints.map(d => d.mz),
                z: unscannedPoints.map(d => d.intensity),
                mode: 'markers',
                marker: {
                    size: 3,
                    color: '#bdc3c7',
                    opacity: 0.4
                },
                type: 'scatter3d',
                name: '未被选择的离子',
                text: unscannedPoints.map(d => `${d.compound_name}<br>RT: ${d.rt}min<br>m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>状态: 未被MS2扫描`),
                hovertemplate: '%{text}<extra></extra>'
            };

            // 被扫描的前体离子（红色高亮）
            const scannedTrace = {
                x: scannedPoints.map(d => d.rt),
                y: scannedPoints.map(d => d.mz),
                z: scannedPoints.map(d => d.intensity),
                mode: 'markers',
                marker: {
                    size: 6,
                    color: '#e74c3c',
                    opacity: 0.9,
                    line: {
                        width: 1,
                        color: '#c0392b'
                    }
                },
                type: 'scatter3d',
                name: '被选择的前体离子',
                text: scannedPoints.map(d => `${d.compound_name}<br>RT: ${d.rt}min<br>前体m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>状态: 已被MS2扫描`),
                hovertemplate: '%{text}<extra></extra>'
            };

            const layout = {
                title: {
                    text: `MS1三维数据 + MS2前体离子选择 (Top${topN}, 排除${exclusionTime}s)`,
                    font: { size: 18, color: '#2c3e50' }
                },
                scene: {
                    xaxis: { title: '保留时间 (min)' },
                    yaxis: { title: '质荷比 (m/z)' },
                    zaxis: { title: '强度' },
                    camera: {
                        eye: { x: 1.5, y: 1.5, z: 1.5 }
                    }
                },
                margin: { l: 0, r: 0, b: 0, t: 50 },
                showlegend: true,
                legend: {
                    x: 0.02,
                    y: 0.98,
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                }
            };

            Plotly.newPlot('ms1Plot', [unscannedTrace, scannedTrace], layout, {responsive: true});
        }

        // 绘制MS2碎片离子谱图
        function plotMS2FragmentSpectrum() {
            if (!ms2Data || ms2Data.length === 0) return;

            // 选择一个代表性的MS2谱图进行展示
            const selectedPrecursor = ms2Data[Math.floor(ms2Data.length / 2)];

            // 模拟碎片离子数据
            const fragmentData = generateFragmentSpectrum(selectedPrecursor);

            // 为每个碎片离子创建垂直线条
            const traces = fragmentData.map(d => ({
                x: [d.mz, d.mz],
                y: [0, d.intensity],
                type: 'scatter',
                mode: 'lines',
                line: {
                    color: '#3498db',
                    width: 2
                },
                showlegend: false,
                hoverinfo: 'none'
            }));

            // 添加顶部的点用于悬停信息
            const pointTrace = {
                x: fragmentData.map(d => d.mz),
                y: fragmentData.map(d => d.intensity),
                type: 'scatter',
                mode: 'markers',
                marker: {
                    color: '#3498db',
                    size: 6,
                    opacity: 0.8
                },
                name: '碎片离子',
                text: fragmentData.map(d => `m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>类型: ${d.type || '未知'}`),
                hovertemplate: '%{text}<extra></extra>'
            };

            traces.push(pointTrace);

            const layout = {
                title: {
                    text: `MS2碎片离子谱图 (前体离子: ${selectedPrecursor.precursor_mz.toFixed(4)} m/z)`,
                    font: { size: 18, color: '#2c3e50' }
                },
                xaxis: { title: '质荷比 (m/z)' },
                yaxis: { title: '相对强度 (%)' },
                margin: { l: 60, r: 20, b: 60, t: 50 },
                showlegend: false
            };

            Plotly.newPlot('ms2Plot', traces, layout, {responsive: true});
        }

        // 生成模拟的碎片离子谱图
        function generateFragmentSpectrum(precursor) {
            const fragments = [];
            const precursorMz = precursor.precursor_mz;
            const baseIntensity = precursor.intensity;

            // 生成一些典型的碎片离子
            const fragmentTypes = [
                { loss: 18, type: 'H2O丢失', probability: 0.8 },
                { loss: 17, type: 'NH3丢失', probability: 0.6 },
                { loss: 28, type: 'CO丢失', probability: 0.7 },
                { loss: 44, type: 'CO2丢失', probability: 0.5 },
                { loss: 46, type: 'HCOOH丢失', probability: 0.4 }
            ];

            fragmentTypes.forEach(frag => {
                if (Math.random() < frag.probability) {
                    const fragMz = precursorMz - frag.loss;
                    if (fragMz > 50) { // 确保碎片离子m/z合理
                        fragments.push({
                            mz: fragMz,
                            intensity: baseIntensity * (0.3 + Math.random() * 0.7),
                            type: frag.type
                        });
                    }
                }
            });

            // 添加一些随机碎片
            for (let i = 0; i < 5 + Math.floor(Math.random() * 10); i++) {
                const fragMz = 50 + Math.random() * (precursorMz - 50);
                fragments.push({
                    mz: fragMz,
                    intensity: baseIntensity * Math.random() * 0.5,
                    type: '其他碎片'
                });
            }

            // 按m/z排序并标准化强度
            fragments.sort((a, b) => a.mz - b.mz);
            const maxIntensity = Math.max(...fragments.map(f => f.intensity));
            fragments.forEach(f => f.intensity = (f.intensity / maxIntensity) * 100);

            return fragments;
        }



        // 更新参数显示
        function updateParameterDisplay() {
            document.getElementById('rtValue').textContent = currentParams.rtRange;
            document.getElementById('compoundsValue').textContent = currentParams.compounds;
            document.getElementById('exclusionValue').textContent = currentParams.exclusionTime;
        }

        // 更新数据和图表
        function updateData() {
            // 获取当前参数
            currentParams.topN = parseInt(document.getElementById('topN').value);
            currentParams.exclusionTime = parseFloat(document.getElementById('exclusionTime').value);
            currentParams.rtRange = parseFloat(document.getElementById('rtRange').value);
            currentParams.compounds = parseInt(document.getElementById('compounds').value);

            updateParameterDisplay();

            // 重新生成数据
            generateMS1Data();
            generateMS2Data();

            // 重新绘制图表
            plotMS1Data();
            plotMS2FragmentSpectrum();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateData();

            // 添加范围滑块的实时更新
            document.getElementById('rtRange').addEventListener('input', function() {
                document.getElementById('rtValue').textContent = this.value;
            });

            document.getElementById('compounds').addEventListener('input', function() {
                document.getElementById('compoundsValue').textContent = this.value;
            });
        });
    </script>
</body>
</html>
