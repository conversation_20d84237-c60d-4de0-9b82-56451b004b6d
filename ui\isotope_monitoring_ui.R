# UI层 - 同位素内标监控界面
# 提供同位素内标监控的用户界面组件

# 同位素内标监控UI模块
isotope_monitoring_ui <- function() {
  tagList(
    # 同位素内标监控面板
    div(class = "card mb-4",
      div(class = "card-header d-flex justify-content-between align-items-center collapsible-header",
        onclick = "toggleCollapse('isotope_monitoring_content')",
        h5(class = "mb-0", 
          icon("atom"), " 同位素内标监控",
          span(class = "badge badge-info ml-2", id = "isotope_status_badge", "未运行")
        ),
        span(class = "collapse-icon", id = "isotope_monitoring_icon", "▼")
      ),
      div(id = "isotope_monitoring_content", class = "card-body module-content",
        
        # 控制面板
        div(class = "row mb-3",
          div(class = "col-md-6",
            h6("分析参数"),
            div(class = "form-group",
              label("质量容差 (ppm):", `for` = "isotope_mass_tolerance"),
              numericInput("isotope_mass_tolerance", 
                          label = NULL,
                          value = 10, 
                          min = 1, 
                          max = 100, 
                          step = 1)
            ),
            div(class = "form-check",
              checkboxInput("isotope_include_ms2", 
                           "包含MS2-MS1匹配分析", 
                           value = TRUE)
            )
          ),
          div(class = "col-md-6",
            h6("样本选择"),
            div(class = "form-group",
              label("选择样本文件:", `for` = "isotope_file_selection"),
              selectInput("isotope_file_selection",
                         label = NULL,
                         choices = list("所有文件" = "all"),
                         multiple = TRUE,
                         selected = "all")
            )
          )
        ),
        
        # 操作按钮
        div(class = "row mb-3",
          div(class = "col-12",
            actionButton("run_isotope_analysis", 
                        "开始同位素内标分析", 
                        class = "btn btn-primary mr-2",
                        icon = icon("play")),
            actionButton("refresh_isotope_data", 
                        "刷新数据", 
                        class = "btn btn-secondary mr-2",
                        icon = icon("refresh")),
            downloadButton("download_isotope_results", 
                          "下载结果", 
                          class = "btn btn-success",
                          icon = icon("download"))
          )
        ),
        
        # 分析状态显示
        div(class = "row mb-3",
          div(class = "col-12",
            div(id = "isotope_analysis_status", 
                class = "alert alert-info", 
                style = "display: none;",
                "分析状态将在这里显示...")
          )
        ),
        
        # 结果摘要
        div(class = "row mb-3",
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_compounds", "0", class = "text-primary"),
                p("监控化合物数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_samples", "0", class = "text-info"),
                p("分析样本数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_total_points", "0", class = "text-success"),
                p("数据点总数", class = "mb-0")
              )
            )
          ),
          div(class = "col-md-3",
            div(class = "card bg-light",
              div(class = "card-body text-center",
                h4(id = "isotope_ms2_matched", "0", class = "text-warning"),
                p("MS2匹配点数", class = "mb-0")
              )
            )
          )
        ),
        
        # 标签页面板
        tabsetPanel(id = "isotope_tabs",
          
          # 3D散点图标签页
          tabPanel("3D散点图", 
            value = "scatter_plot",
            div(class = "mt-3",
              h6("同位素内标MS1三维散点图"),
              p("X轴: 保留时间, Y轴: 样本名称, Z轴: 强度, 颜色: m/z偏差, 形状: MS2存在性"),
              div(style = "height: 600px;",
                plotlyOutput("isotope_3d_scatter", height = "100%")
              )
            )
          ),
          
          # MS2谱图标签页
          tabPanel("MS2谱图", 
            value = "ms2_spectrum",
            div(class = "mt-3",
              div(class = "row mb-3",
                div(class = "col-md-6",
                  selectInput("ms2_spectrum_selection",
                             "选择MS2谱图:",
                             choices = list("请先运行分析" = ""),
                             width = "100%")
                ),
                div(class = "col-md-6",
                  actionButton("load_ms2_spectrum", 
                              "加载谱图", 
                              class = "btn btn-primary",
                              icon = icon("chart-line"))
                )
              ),
              div(style = "height: 500px;",
                plotlyOutput("isotope_ms2_spectrum", height = "100%")
              )
            )
          ),
          
          # 信息表格标签页
          tabPanel("信息表格", 
            value = "info_table",
            div(class = "mt-3",
              h6("同位素内标详细信息"),
              p("包含保留时间、m/z偏差、峰形参数、信噪比等详细信息"),
              div(class = "table-responsive",
                DT::dataTableOutput("isotope_info_table")
              )
            )
          ),
          
          # 质量控制标签页
          tabPanel("质量控制", 
            value = "quality_control",
            div(class = "mt-3",
              h6("数据质量评估"),
              div(class = "row",
                div(class = "col-md-6",
                  h6("质量问题统计"),
                  div(id = "quality_issues_summary",
                    "运行分析后显示质量问题统计..."
                  )
                ),
                div(class = "col-md-6",
                  h6("质量控制图表"),
                  div(style = "height: 400px;",
                    plotlyOutput("isotope_quality_plot", height = "100%")
                  )
                )
              )
            )
          )
        )
      )
    )
  )
}

# 同位素内标监控服务器逻辑
isotope_monitoring_server <- function(input, output, session) {
  
  # 响应式变量
  isotope_analysis_results <- reactiveVal(NULL)
  isotope_processor_initialized <- reactiveVal(FALSE)
  
  # 初始化处理器
  observe({
    project_root <- get_project_root_path()
    if (!is.null(project_root)) {
      db_path <- file.path(project_root, "data", "spectra.db")
      if (file.exists(db_path)) {
        tryCatch({
          source("server/isotope_monitoring_server.R", encoding = "UTF-8")
          initialize_isotope_processor(db_path)
          isotope_processor_initialized(TRUE)
          updateSelectInput(session, "isotope_status_badge", 
                           choices = list("已就绪" = "ready"))
        }, error = function(e) {
          log_error(paste("初始化同位素监控处理器失败:", e$message))
        })
      }
    }
  })
  
  # 更新文件选择列表
  observe({
    if (isotope_processor_initialized()) {
      project_root <- get_project_root_path()
      if (!is.null(project_root)) {
        db_path <- file.path(project_root, "data", "spectra.db")
        if (file.exists(db_path)) {
          tryCatch({
            con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
            files <- DBI::dbGetQuery(con, "SELECT DISTINCT file_id, file_name FROM data_files ORDER BY file_name")
            DBI::dbDisconnect(con)
            
            if (nrow(files) > 0) {
              choices <- as.list(files$file_id)
              names(choices) <- files$file_name
              choices <- c(list("所有文件" = "all"), choices)
              
              updateSelectInput(session, "isotope_file_selection", 
                               choices = choices, 
                               selected = "all")
            }
          }, error = function(e) {
            log_error(paste("更新文件列表失败:", e$message))
          })
        }
      }
    }
  })
  
  # 运行同位素内标分析
  observeEvent(input$run_isotope_analysis, {
    if (!isotope_processor_initialized()) {
      showNotification("同位素监控处理器未初始化", type = "error")
      return()
    }
    
    # 显示分析状态
    shinyjs::show("isotope_analysis_status")
    shinyjs::html("isotope_analysis_status", "正在运行同位素内标分析...")
    shinyjs::removeClass("isotope_analysis_status", "alert-info alert-success alert-danger")
    shinyjs::addClass("isotope_analysis_status", "alert-info")
    
    # 准备参数
    file_ids <- NULL
    if (!"all" %in% input$isotope_file_selection) {
      file_ids <- input$isotope_file_selection
    }
    
    tolerance_ppm <- input$isotope_mass_tolerance
    include_ms2 <- input$isotope_include_ms2
    
    # 异步执行分析
    future({
      processor <- get_isotope_processor()
      if (!is.null(processor)) {
        processor$extractor$mass_tolerance_ppm <- tolerance_ppm
        processor$process_isotope_monitoring(file_ids, include_ms2)
      } else {
        list(success = FALSE, message = "处理器未初始化")
      }
    }) %...>% {
      # 处理分析结果
      if (.$success) {
        isotope_analysis_results(.$data)
        
        # 更新状态显示
        shinyjs::html("isotope_analysis_status", paste("分析完成:", .$message))
        shinyjs::removeClass("isotope_analysis_status", "alert-info alert-danger")
        shinyjs::addClass("isotope_analysis_status", "alert-success")
        
        # 更新摘要统计
        summary <- .$data$summary
        shinyjs::html("isotope_total_compounds", as.character(summary$total_compounds))
        shinyjs::html("isotope_total_samples", as.character(summary$total_samples))
        shinyjs::html("isotope_total_points", as.character(summary$total_data_points))
        shinyjs::html("isotope_ms2_matched", as.character(summary$ms2_matched_points))
        
        showNotification("同位素内标分析完成", type = "success")
      } else {
        shinyjs::html("isotope_analysis_status", paste("分析失败:", .$message))
        shinyjs::removeClass("isotope_analysis_status", "alert-info alert-success")
        shinyjs::addClass("isotope_analysis_status", "alert-danger")
        
        showNotification(paste("分析失败:", .$message), type = "error")
      }
    }
  })

  # 渲染3D散点图
  output$isotope_3d_scatter <- renderPlotly({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$scatter_plot)) {
      return(plotly_empty() %>%
             layout(title = "请先运行同位素内标分析"))
    }

    return(results$scatter_plot)
  })

  # 更新MS2谱图选择列表
  observe({
    results <- isotope_analysis_results()
    if (!is.null(results) && !is.null(results$raw_data)) {
      # 查找有MS2数据的点
      ms2_data <- results$raw_data[results$raw_data$has_ms2 == TRUE, ]

      if (nrow(ms2_data) > 0) {
        choices <- list()
        for (i in 1:nrow(ms2_data)) {
          row <- ms2_data[i, ]
          spectrum_ids <- strsplit(row$ms2_spectrum_ids, ",")[[1]]

          for (spectrum_id in spectrum_ids) {
            label <- paste0(row$compound_name, " - ", row$file_name,
                           " (RT: ", round(row$rtime, 2), "min)")
            choices[[label]] <- spectrum_id
          }
        }

        updateSelectInput(session, "ms2_spectrum_selection",
                         choices = choices)
      }
    }
  })

  # 加载MS2谱图
  observeEvent(input$load_ms2_spectrum, {
    spectrum_id <- input$ms2_spectrum_selection
    if (is.null(spectrum_id) || spectrum_id == "") {
      showNotification("请选择一个MS2谱图", type = "warning")
      return()
    }

    tryCatch({
      processor <- get_isotope_processor()
      if (!is.null(processor)) {
        ms2_data <- processor$get_ms2_spectrum(as.numeric(spectrum_id))

        if (!is.null(ms2_data) && !is.null(ms2_data$peaks) && nrow(ms2_data$peaks) > 0) {
          output$isotope_ms2_spectrum <- renderPlotly({
            processor$visualizer$create_ms2_spectrum_plot(ms2_data)
          })
        } else {
          showNotification("无法加载MS2谱图数据", type = "error")
        }
      }
    }, error = function(e) {
      showNotification(paste("加载MS2谱图失败:", e$message), type = "error")
    })
  })

  # 渲染信息表格
  output$isotope_info_table <- DT::renderDataTable({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$info_table)) {
      return(data.frame(消息 = "请先运行同位素内标分析"))
    }

    table_data <- results$info_table

    # 为有质量问题的行添加样式
    if (nrow(table_data) > 0) {
      # 创建行样式
      row_styles <- rep("", nrow(table_data))
      problem_rows <- which(table_data$质量问题 != "")
      row_styles[problem_rows] <- "background-color: #fff3cd !important;"

      DT::datatable(
        table_data,
        options = list(
          pageLength = 25,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel'),
          columnDefs = list(
            list(targets = "_all", className = "dt-center")
          )
        ),
        extensions = 'Buttons',
        rownames = FALSE
      ) %>%
      DT::formatStyle(
        columns = 1:ncol(table_data),
        valueColumns = "质量问题",
        backgroundColor = DT::styleEqual("", "white", "rgba(255, 243, 205, 0.5)")
      ) %>%
      DT::formatRound(
        columns = c("保留时间_min", "mz偏差_ppm", "峰形质量", "半峰宽_min", "信噪比"),
        digits = 3
      )
    } else {
      DT::datatable(table_data, options = list(pageLength = 25))
    }
  })

  # 渲染质量控制图表
  output$isotope_quality_plot <- renderPlotly({
    results <- isotope_analysis_results()
    if (is.null(results) || is.null(results$info_table)) {
      return(plotly_empty() %>%
             layout(title = "请先运行同位素内标分析"))
    }

    table_data <- results$info_table

    if (nrow(table_data) > 0) {
      # 创建质量控制散点图（保留时间 vs m/z偏差）
      p <- plot_ly(
        data = table_data,
        x = ~保留时间_min,
        y = ~mz偏差_ppm,
        color = ~质量问题 != "",
        colors = c("green", "red"),
        text = ~paste(
          "化合物:", 化合物名称, "<br>",
          "样本:", 样本名称, "<br>",
          "保留时间:", 保留时间_min, "min<br>",
          "m/z偏差:", mz偏差_ppm, "ppm<br>",
          "信噪比:", 信噪比, "<br>",
          if (质量问题 != "") paste("问题:", 质量问题) else "正常"
        ),
        hovertemplate = "%{text}<extra></extra>",
        type = "scatter",
        mode = "markers",
        marker = list(size = 8)
      ) %>%
      layout(
        title = "质量控制图 - 保留时间 vs m/z偏差",
        xaxis = list(title = "保留时间 (min)"),
        yaxis = list(title = "m/z偏差 (ppm)"),
        showlegend = TRUE
      )

      return(p)
    } else {
      return(plotly_empty() %>%
             layout(title = "没有数据可显示"))
    }
  })

  # 更新质量问题摘要
  observe({
    results <- isotope_analysis_results()
    if (!is.null(results) && !is.null(results$info_table)) {
      table_data <- results$info_table

      if (nrow(table_data) > 0) {
        total_samples <- nrow(table_data)
        problem_samples <- sum(table_data$质量问题 != "")
        normal_samples <- total_samples - problem_samples

        # 统计各种问题类型
        all_problems <- table_data$质量问题[table_data$质量问题 != ""]
        problem_types <- unlist(strsplit(all_problems, "; "))
        problem_counts <- table(problem_types)

        summary_html <- paste0(
          "<div class='row'>",
          "<div class='col-md-6'>",
          "<h6>总体统计</h6>",
          "<ul>",
          "<li>总样本数: ", total_samples, "</li>",
          "<li>正常样本: ", normal_samples, " (", round(normal_samples/total_samples*100, 1), "%)</li>",
          "<li>问题样本: ", problem_samples, " (", round(problem_samples/total_samples*100, 1), "%)</li>",
          "</ul>",
          "</div>",
          "<div class='col-md-6'>",
          "<h6>问题类型统计</h6>",
          "<ul>"
        )

        if (length(problem_counts) > 0) {
          for (i in 1:length(problem_counts)) {
            problem_name <- names(problem_counts)[i]
            problem_count <- problem_counts[i]
            summary_html <- paste0(summary_html,
                                  "<li>", problem_name, ": ", problem_count, "</li>")
          }
        } else {
          summary_html <- paste0(summary_html, "<li>无质量问题</li>")
        }

        summary_html <- paste0(summary_html, "</ul></div></div>")

        shinyjs::html("quality_issues_summary", summary_html)
      }
    }
  })

  # 下载结果
  output$download_isotope_results <- downloadHandler(
    filename = function() {
      paste0("isotope_monitoring_results_", Sys.Date(), ".xlsx")
    },
    content = function(file) {
      results <- isotope_analysis_results()
      if (!is.null(results) && !is.null(results$info_table)) {
        # 使用openxlsx包创建Excel文件
        if (!requireNamespace("openxlsx", quietly = TRUE)) {
          install.packages("openxlsx")
        }

        wb <- openxlsx::createWorkbook()

        # 添加信息表格工作表
        openxlsx::addWorksheet(wb, "同位素内标信息")
        openxlsx::writeData(wb, "同位素内标信息", results$info_table)

        # 添加摘要工作表
        if (!is.null(results$summary)) {
          summary_df <- data.frame(
            项目 = c("监控化合物数", "分析样本数", "数据点总数", "MS2匹配点数"),
            数值 = c(results$summary$total_compounds,
                    results$summary$total_samples,
                    results$summary$total_data_points,
                    results$summary$ms2_matched_points)
          )
          openxlsx::addWorksheet(wb, "分析摘要")
          openxlsx::writeData(wb, "分析摘要", summary_df)
        }

        openxlsx::saveWorkbook(wb, file, overwrite = TRUE)
      }
    }
  )
}
}
