<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LC-MSMS (DDA) 教学工具集</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            width: 90%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            animation: fadeInUp 1s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 20px,
                rgba(255,255,255,0.05) 20px,
                rgba(255,255,255,0.05) 40px
            );
            animation: slide 30s linear infinite;
        }
        
        @keyframes slide {
            0% { transform: translateX(-100px); }
            100% { transform: translateX(100px); }
        }
        
        .header h1 {
            font-size: 3.5em;
            font-weight: 300;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            font-size: 1.4em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .content {
            padding: 60px 40px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .tool-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .tool-card:hover::before {
            left: 100%;
        }
        
        .tool-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .tool-icon {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
        }
        
        .tool-title {
            font-size: 1.8em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .tool-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .tool-features {
            text-align: left;
            margin-bottom: 30px;
        }
        
        .tool-features ul {
            list-style: none;
            padding: 0;
        }
        
        .tool-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .tool-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }
        
        .launch-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .launch-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .launch-btn:hover::before {
            left: 100%;
        }
        
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }
        
        .info-section {
            background: linear-gradient(135deg, #e8f4f8 0%, #d6eaf8 100%);
            border-radius: 15px;
            padding: 40px;
            margin-top: 40px;
        }
        
        .info-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .info-item {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .info-item h3 {
            color: #3498db;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            border-top: 1px solid #ecf0f1;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .header p {
                font-size: 1.2em;
            }
            
            .content {
                padding: 40px 20px;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .tool-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LC-MSMS (DDA) 教学工具集</h1>
            <p>液相色谱-串联质谱数据依赖采集模式的交互式学习平台<br>
            理解MS1无筛选扫描与MS2有筛选扫描的本质区别，掌握TopN策略和动态排除原理</p>
        </div>
        
        <div class="content">
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-icon">📊</div>
                    <div class="tool-title">基础版教学工具</div>
                    <div class="tool-description">
                        适合初学者的LC-MSMS数据可视化工具，提供基础的3D和2D数据展示功能。
                    </div>
                    <div class="tool-features">
                        <ul>
                            <li>MS1三维数据 + MS2前体离子选择可视化</li>
                            <li>MS2碎片离子谱图展示</li>
                            <li>TopN策略和动态排除演示</li>
                            <li>实时参数调节（支持1秒动态排除）</li>
                            <li>仪器工作原理图</li>
                        </ul>
                    </div>
                    <a href="lc_msms_interactive.html" class="launch-btn">启动基础版</a>
                </div>
                
                <div class="tool-card">
                    <div class="tool-icon">🔬</div>
                    <div class="tool-title">高级版教学工具</div>
                    <div class="tool-description">
                        基于真实代谢物数据的高级教学平台，提供全面的数据分析和对比功能。
                    </div>
                    <div class="tool-features">
                        <ul>
                            <li>真实代谢物数据库</li>
                            <li>多标签页界面</li>
                            <li>MS1三维数据 + MS2前体离子选择集成</li>
                            <li>MS2碎片离子谱图详细分析</li>
                            <li>数据统计分析</li>
                            <li>DDA vs DIA对比研究</li>
                        </ul>
                    </div>
                    <a href="advanced_lc_msms.html" class="launch-btn">启动高级版</a>
                </div>
            </div>
            
            <div class="info-section">
                <h2>🎯 学习目标</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <h3>理解数据结构</h3>
                        <p>掌握MS1和MS2数据的三维结构特征，理解保留时间、质荷比和强度的关系。</p>
                    </div>
                    <div class="info-item">
                        <h3>掌握DDA原理</h3>
                        <p>深入理解质谱仪在MS1和MS2模式间的切换机制，以及TopN策略和动态排除的工作原理。</p>
                    </div>
                    <div class="info-item">
                        <h3>区分扫描特性</h3>
                        <p>理解MS1"相对连续"（无筛选）与MS2"不连续"（有筛选）的本质区别。</p>
                    </div>
                    <div class="info-item">
                        <h3>优化实验设计</h3>
                        <p>学习如何根据样品特性和分析目标选择最佳的DDA参数组合。</p>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <p>© 2025 LC-MSMS教学工具集 | 仅用于教育目的 | 模拟数据不代表真实实验结果</p>
            </div>
        </div>
    </div>
</body>
</html>
