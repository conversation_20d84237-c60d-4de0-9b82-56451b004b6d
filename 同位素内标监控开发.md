# 同位素内标监控
## 监控指标
EIC，峰型，半高峰宽，rt、mz、intensity、MS2碎片在QC、实验样本中的稳定性，灵敏度(S/N)。
## 同位素化合物信息管理(已完成)
| 化合物名称 | 分子质量 | 保留时间 | 离子类型 | 离子质荷比(m/z) |
| --- | --- | --- | --- | --- |

- 添加方式：手动添加，基于yaml文件导入
- 编辑、删除、刷新
- 导出信息到yaml文件
## 样品中同位素化合物数据提取
从项目 spectra.db 中提取所有离子的EIC、MS2碎片信息
基于同位素内标的yaml配置文件中的信息，去对应的样本原始数据中找到每个同位素化合物的 EIC、MS2碎片，然后计算峰型、半峰宽、RT、MZ、峰面积等信息保存到结果json文件。
## 同位素内标监控结果可视化


输入：
原始数据spectra.db，同位素内标yaml文件，数据列表索引json文件，质荷比容差值ppm

输出，主要为UI界面交互式图标显示的内容：
1、同位素内标MS1三维散点图：同位素内标EIC图，x轴为rtime，y轴为样本，z轴为intensity，颜色为质荷比偏差，形状为是否有MS2碎片扫描结果(该部分若spectra.db还未开发相关对应功能，需开发)；
2、若需开发在数据库中添加MS2与MS1的匹配功能，可参考：
MS2spectraData中会包含precScanNum的信息，该信息为MS1的scanIndex，说明该次MS2扫描的离子选取基于的MS1扫描数据，precursorMz与precursorIntensity即为MS1中对应扫描数据的mz与intensity，由于质谱扫描的不连续性，数据质检可能存在一点偏差，但不会太大。
3、对应MS2扫描的质谱图：x轴为mz，y轴为intensity，并显示precursor信息
4、样本中同位素内标信息表格：该同位素内标在各样本中的rtime(使用intensity最高的扫描点作为rtime)、mz偏差(使用intensity最高的扫描点计算mz偏差)、扫描点mz范围(使用非噪音扫描点计算mz范围)、峰型(使用非噪音扫描点拟合高斯分布并计算评估峰型)、半峰宽(使用非噪音扫描点拟合高斯分布并计算评估半峰宽)、峰面积(使用非噪音扫描点拟合高斯分布并计算评估峰面积)、信噪比等
5、高亮信息表格中的显示异常数据
