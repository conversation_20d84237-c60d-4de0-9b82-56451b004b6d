<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LC-MSMS (DDA) 高级数据结构教学</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="sample_data_generator.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 50px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: slide 20s linear infinite;
        }
        
        @keyframes slide {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .nav-tabs {
            background: #34495e;
            padding: 0;
            display: flex;
            justify-content: center;
        }
        
        .nav-tab {
            background: transparent;
            color: white;
            border: none;
            padding: 15px 30px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-tab:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .nav-tab.active {
            background: white;
            color: #2c3e50;
            border-bottom-color: #3498db;
        }
        
        .content {
            padding: 30px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-5px);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        
        .plot-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        .controls-panel {
            background: linear-gradient(135deg, #e8f4f8 0%, #d6eaf8 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            border: 1px solid #3498db;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .control-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .control-group input:focus, .control-group select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }
        
        .info-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info-card h3 {
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            display: block;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .workflow-diagram {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
        }
        
        .workflow-step {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 50px;
            margin: 10px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .workflow-step:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .workflow-arrow {
            font-size: 2.5em;
            color: #3498db;
            margin: 0 15px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        
        .metabolite-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metabolite-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .metabolite-card:hover {
            transform: translateX(5px);
        }
        
        .metabolite-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .metabolite-info {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .workflow-diagram {
                flex-direction: column;
            }
            
            .workflow-arrow {
                transform: rotate(90deg);
                margin: 15px 0;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .control-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LC-MSMS (DDA) 高级数据结构教学</h1>
            <p>基于真实代谢物数据的液相色谱-串联质谱交互式学习平台</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('theory')">📚 理论基础</button>
            <button class="nav-tab" onclick="showTab('visualization')">📊 数据可视化</button>
            <button class="nav-tab" onclick="showTab('comparison')">⚖️ 对比研究</button>
        </div>

        <div class="content">
            <!-- 理论基础标签页 -->
            <div id="theory" class="tab-content active">
                <div class="section">
                    <h2>🔬 LC-MSMS (DDA) 工作原理</h2>

                    <div class="workflow-diagram">
                        <div class="workflow-step">样品注入</div>
                        <div class="workflow-arrow">→</div>
                        <div class="workflow-step">液相色谱分离</div>
                        <div class="workflow-arrow">→</div>
                        <div class="workflow-step">电离源 (ESI)</div>
                        <div class="workflow-arrow">→</div>
                        <div class="workflow-step" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">质谱仪</div>
                    </div>

                    <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 5px solid #3498db;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🔄 DDA扫描循环模式</h4>
                        <div style="display: flex; justify-content: space-around; align-items: center; flex-wrap: wrap;">
                            <div style="text-align: center; margin: 10px;">
                                <div style="background: #3498db; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">MS1 全扫描</div>
                                <div style="font-size: 0.9em; color: #7f8c8d;">无筛选，获取所有离子</div>
                            </div>
                            <div style="font-size: 2em; color: #3498db;">↓</div>
                            <div style="text-align: center; margin: 10px;">
                                <div style="background: #e67e22; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">数据处理</div>
                                <div style="font-size: 0.9em; color: #7f8c8d;">TopN选择 + 动态排除</div>
                            </div>
                            <div style="font-size: 2em; color: #3498db;">↓</div>
                            <div style="text-align: center; margin: 10px;">
                                <div style="background: #e74c3c; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 10px; font-weight: bold;">MS2 碎裂扫描</div>
                                <div style="font-size: 0.9em; color: #7f8c8d;">有筛选，仅扫描选中离子</div>
                            </div>
                            <div style="font-size: 2em; color: #3498db;">↺</div>
                        </div>
                        <div style="text-align: center; margin-top: 15px; font-style: italic; color: #7f8c8d;">
                            质谱仪在MS1和MS2模式间快速切换，每个时刻只能执行一种扫描
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>数据依赖采集 (DDA) 核心概念</h3>
                        <p><strong>MS1扫描：</strong>质谱仪对所有进入的离子进行全扫描，无筛选行为，获得完整的质谱图</p>
                        <p><strong>数据处理：</strong>实时分析MS1数据，根据TopN策略选择强度最高的N个离子，并应用动态排除规则</p>
                        <p><strong>MS2扫描：</strong>质谱仪切换到MS2模式，仅对选中的前体离子进行碎裂扫描</p>
                        <p><strong>循环模式：</strong>质谱仪在MS1和MS2模式间快速切换，每个时刻只能执行一种扫描模式</p>
                        <p><strong>扫描特性：</strong>MS1相对"连续"（无筛选），MS2"不连续"（有筛选+动态排除）</p>
                    </div>
                </div>

                <div class="section">
                    <h2>📋 常见代谢物数据库</h2>
                    <div class="metabolite-list" id="metaboliteList">
                        <!-- 动态生成代谢物列表 -->
                    </div>
                </div>

                <div class="section">
                    <h2>📈 数据特征对比</h2>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <span class="stat-number">3D</span>
                            <div class="stat-label">MS1 数据维度<br>RT × m/z × Intensity</div>
                        </div>
                        <div class="stat-box">
                            <span class="stat-number">相对连续</span>
                            <div class="stat-label">MS1 扫描模式<br>无筛选，高斯分布色谱峰</div>
                        </div>
                        <div class="stat-box">
                            <span class="stat-number">3D</span>
                            <div class="stat-label">MS2 数据维度<br>Precursor × m/z × Intensity</div>
                        </div>
                        <div class="stat-box">
                            <span class="stat-number">不连续</span>
                            <div class="stat-label">MS2 扫描模式<br>有筛选，TopN + 动态排除</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据可视化标签页 -->
            <div id="visualization" class="tab-content">
                <div class="section">
                    <h2>🎛️ 实验参数控制</h2>
                    <div class="controls-panel">
                        <h3>调整DDA参数观察数据变化</h3>
                        <div class="control-grid">
                            <div class="control-group">
                                <label for="topN">TopN值:</label>
                                <select id="topN" onchange="updateVisualization()">
                                    <option value="1">Top 1</option>
                                    <option value="2" selected>Top 2</option>
                                    <option value="3">Top 3</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <label for="exclusionTime">动态排除时间:</label>
                                <input type="range" id="exclusionTime" min="0" max="60" value="10" onchange="updateVisualization()">
                                <span id="exclusionValue">10</span> 秒
                            </div>

                            <div class="control-group">
                                <label for="rtRange">分析时间范围:</label>
                                <input type="range" id="rtRange" min="10" max="30" value="20" onchange="updateVisualization()">
                                <span id="rtValue">20</span> 分钟
                            </div>

                            <div class="control-group">
                                <label for="noiseLevel">噪声水平:</label>
                                <input type="range" id="noiseLevel" min="0" max="0.3" step="0.05" value="0.1" onchange="updateVisualization()">
                                <span id="noiseValue">0.1</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>📊 MS1 三维数据可视化</h2>
                    <div class="info-card">
                        <h3>MS1 数据 + MS2前体离子选择可视化</h3>
                        <p>X轴：保留时间(RT, min) | Y轴：质荷比(m/z) | Z轴：强度(Intensity)</p>
                        <p>MS1扫描无筛选行为，每个代谢物在色谱维度呈现高斯分布</p>
                        <p><strong>🔴 红色点：</strong>被TopN策略选择进行MS2扫描的前体离子</p>
                        <p><strong>⚪ 灰色点：</strong>未被选择的离子（被TopN策略或动态排除过滤）</p>
                        <p>直观展示DDA选择性扫描在三维空间中的分布效果</p>
                    </div>
                    <div class="plot-container">
                        <div id="ms1Plot3D" style="width:100%;height:700px;"></div>
                    </div>
                </div>

                <div class="section">
                    <h2>🧪 MS2 碎片离子谱图</h2>
                    <div class="info-card">
                        <h3>MS2 碎片谱图说明</h3>
                        <p>展示选中前体离子经过碰撞诱导解离(CID)后产生的碎片离子</p>
                        <p>不同的碎片离子模式有助于化合物结构鉴定和确认</p>
                        <p><strong>常见碎片类型：</strong>H₂O丢失(-18)、NH₃丢失(-17)、CO₂丢失(-44)等</p>
                    </div>
                    <div class="plot-container">
                        <div id="ms2Plot" style="width:100%;height:500px;"></div>
                    </div>
                </div>
            </div>



            <!-- 对比研究标签页 -->
            <div id="comparison" class="tab-content">
                <div class="section">
                    <h2>⚖️ DDA vs DIA 对比</h2>
                    <div class="info-card">
                        <h3>数据依赖采集 (DDA) vs 数据独立采集 (DIA)</h3>
                        <p><strong>DDA优势：</strong>高质量MS2谱图，适合未知化合物鉴定</p>
                        <p><strong>DDA劣势：</strong>随机性强，重现性差，低丰度化合物易丢失</p>
                        <p><strong>DIA优势：</strong>高重现性，全面覆盖，适合定量分析</p>
                        <p><strong>DIA劣势：</strong>数据复杂，需要谱库支持</p>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let dataGenerator;
        let currentData = null;
        let currentParams = {
            topN: 2,
            exclusionTime: 10,
            rtRange: 15,
            noiseLevel: 0.1
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            dataGenerator = new LCMSMSDataGenerator();
            initializeMetaboliteList();
            updateVisualization();

            // 添加滑块实时更新
            document.getElementById('rtRange').addEventListener('input', function() {
                document.getElementById('rtValue').textContent = this.value;
            });

            document.getElementById('noiseLevel').addEventListener('input', function() {
                document.getElementById('noiseValue').textContent = this.value;
            });
            document.getElementById('exclusionTime').addEventListener('input', function() {
                document.getElementById('exclusionValue').textContent = this.value;
            });
        });

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签页按钮的active类
            const tabButtons = document.querySelectorAll('.nav-tab');
            tabButtons.forEach(button => button.classList.remove('active'));

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 激活对应的标签页按钮
            const activeButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (activeButton) {
                activeButton.classList.add('active');
            }

            // 如果切换到数据可视化标签页，重新绘制图表
            if (tabName === 'visualization' && currentData) {
                setTimeout(() => {
                    plotMS1Data3D();
                    plotMS2Data();
                }, 100);
            }
        }

        // 初始化代谢物列表
        function initializeMetaboliteList() {
            const metaboliteList = document.getElementById('metaboliteList');
            const metabolites = dataGenerator.commonMetabolites;

            metaboliteList.innerHTML = '';
            metabolites.forEach(metabolite => {
                const card = document.createElement('div');
                card.className = 'metabolite-card';
                card.innerHTML = `
                    <div class="metabolite-name">${metabolite.name}</div>
                    <div class="metabolite-info">
                        m/z: ${metabolite.mz.toFixed(4)}<br>
                        RT: ${metabolite.rt.toFixed(1)} min<br>
                        强度: ${metabolite.intensity_base.toLocaleString()}
                    </div>
                `;
                metaboliteList.appendChild(card);
            });
        }



        // 更新可视化
        function updateVisualization() {
            // 获取当前参数
            currentParams.topN = parseInt(document.getElementById('topN').value);
            currentParams.exclusionTime = parseFloat(document.getElementById('exclusionTime').value);
            currentParams.rtRange = parseFloat(document.getElementById('rtRange').value);
            currentParams.noiseLevel = parseFloat(document.getElementById('noiseLevel').value);

            // 生成数据
            currentData = dataGenerator.exportData(
                currentParams.rtRange,
                currentParams.topN,
                currentParams.exclusionTime
            );

            // 更新所有图表
            plotMS1Data3D();
            plotMS2Data();
        }

        // 模拟MS2前体离子选择逻辑（高级版）
        function simulateMS2SelectionAdvanced() {
            if (!currentData) return new Set();

            const scannedPrecursors = new Set();
            const excludedPrecursors = new Map(); // 存储排除的离子和排除结束时间

            // 按保留时间排序所有MS1数据点
            const sortedMS1Data = currentData.ms1_data.filter(d => d.compound_id >= 0)
                .sort((a, b) => a.rt - b.rt);

            // 按时间窗口模拟扫描过程
            const timeWindow = 0.1; // 0.1分钟时间窗口
            let currentTime = Math.min(...sortedMS1Data.map(d => d.rt));
            const maxTime = Math.max(...sortedMS1Data.map(d => d.rt));

            while (currentTime <= maxTime) {
                // 获取当前时间窗口内的所有离子
                const currentIons = sortedMS1Data.filter(d =>
                    d.rt >= currentTime && d.rt < currentTime + timeWindow
                );

                if (currentIons.length > 0) {
                    // 移除已过期的动态排除
                    for (let [key, excludeEndTime] of excludedPrecursors.entries()) {
                        if (currentTime >= excludeEndTime) {
                            excludedPrecursors.delete(key);
                        }
                    }

                    // 过滤掉被动态排除的离子
                    const availableIons = currentIons.filter(d => {
                        const key = `${d.mz.toFixed(4)}`;
                        return !excludedPrecursors.has(key);
                    });

                    // 按强度排序，选择TopN
                    const selectedIons = availableIons
                        .sort((a, b) => b.intensity - a.intensity)
                        .slice(0, currentParams.topN);

                    // 记录被选择的离子
                    selectedIons.forEach(d => {
                        const key = `${d.rt.toFixed(1)}_${d.mz.toFixed(4)}`;
                        scannedPrecursors.add(key);

                        // 添加到动态排除列表
                        const excludeKey = `${d.mz.toFixed(4)}`;
                        const excludeEndTime = currentTime + currentParams.exclusionTime / 60; // 转换为分钟
                        excludedPrecursors.set(excludeKey, excludeEndTime);
                    });
                }

                currentTime += timeWindow;
            }

            return scannedPrecursors;
        }

        // 绘制MS1 3D数据（集成MS2前体离子选择可视化）
        function plotMS1Data3D() {
            if (!currentData) return;

            const ms1Data = currentData.ms1_data.filter(d => d.compound_id >= 0); // 排除背景噪声

            // 实现MS2前体离子选择逻辑
            const scannedPrecursors = simulateMS2SelectionAdvanced();

            // 分离被扫描和未被扫描的数据点
            const scannedPoints = [];
            const unscannedPoints = [];

            ms1Data.forEach(d => {
                const key = `${d.rt.toFixed(1)}_${d.mz.toFixed(4)}`;
                if (scannedPrecursors.has(key)) {
                    scannedPoints.push(d);
                } else {
                    unscannedPoints.push(d);
                }
            });

            // 未被扫描的离子（灰色）
            const unscannedTrace = {
                x: unscannedPoints.map(d => d.rt),
                y: unscannedPoints.map(d => d.mz),
                z: unscannedPoints.map(d => d.intensity),
                mode: 'markers',
                marker: {
                    size: 3,
                    color: '#bdc3c7',
                    opacity: 0.4
                },
                type: 'scatter3d',
                name: '未被选择的离子',
                text: unscannedPoints.map(d => `${d.compound_name}<br>RT: ${d.rt}min<br>m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>状态: 未被MS2扫描`),
                hovertemplate: '%{text}<extra></extra>'
            };

            // 被扫描的前体离子（红色高亮）
            const scannedTrace = {
                x: scannedPoints.map(d => d.rt),
                y: scannedPoints.map(d => d.mz),
                z: scannedPoints.map(d => d.intensity),
                mode: 'markers',
                marker: {
                    size: 6,
                    color: '#e74c3c',
                    opacity: 0.9,
                    line: {
                        width: 1,
                        color: '#c0392b'
                    }
                },
                type: 'scatter3d',
                name: '被选择的前体离子',
                text: scannedPoints.map(d => `${d.compound_name}<br>RT: ${d.rt}min<br>前体m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>状态: 已被MS2扫描`),
                hovertemplate: '%{text}<extra></extra>'
            };

            const layout = {
                title: {
                    text: `MS1三维数据 + MS2前体离子选择 (Top${currentParams.topN}, 排除${currentParams.exclusionTime}s)`,
                    font: { size: 18, color: '#2c3e50' }
                },
                scene: {
                    xaxis: { title: '保留时间 (min)' },
                    yaxis: { title: '质荷比 (m/z)' },
                    zaxis: { title: '强度' },
                    camera: {
                        eye: { x: 1.5, y: 1.5, z: 1.2 }
                    }
                },
                margin: { l: 0, r: 0, b: 0, t: 50 },
                showlegend: true,
                legend: {
                    x: 0.02,
                    y: 0.98,
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                }
            };

            Plotly.newPlot('ms1Plot3D', [unscannedTrace, scannedTrace], layout, {responsive: true});
        }

        // 绘制MS2碎片离子谱图（高级版）
        function plotMS2Data() {
            if (!currentData || !currentData.ms2_data || currentData.ms2_data.length === 0) return;

            // 选择一个代表性的MS2谱图进行展示
            const selectedPrecursor = currentData.ms2_data[Math.floor(currentData.ms2_data.length / 2)];

            // 模拟碎片离子数据
            const fragmentData = generateFragmentSpectrumAdvanced(selectedPrecursor);

            // 为每个碎片离子创建垂直线条
            const traces = fragmentData.map(d => ({
                x: [d.mz, d.mz],
                y: [0, d.intensity],
                type: 'scatter',
                mode: 'lines',
                line: {
                    color: '#3498db',
                    width: 2
                },
                showlegend: false,
                hoverinfo: 'none'
            }));

            // 添加顶部的点用于悬停信息
            const pointTrace = {
                x: fragmentData.map(d => d.mz),
                y: fragmentData.map(d => d.intensity),
                type: 'scatter',
                mode: 'markers',
                marker: {
                    color: '#3498db',
                    size: 6,
                    opacity: 0.8
                },
                name: '碎片离子',
                text: fragmentData.map(d => `m/z: ${d.mz}<br>强度: ${d.intensity.toFixed(0)}<br>类型: ${d.type || '未知'}`),
                hovertemplate: '%{text}<extra></extra>'
            };

            traces.push(pointTrace);

            const layout = {
                title: {
                    text: `MS2碎片离子谱图 (前体离子: ${selectedPrecursor.precursor_mz.toFixed(4)} m/z)`,
                    font: { size: 18, color: '#2c3e50' }
                },
                xaxis: { title: '质荷比 (m/z)' },
                yaxis: { title: '相对强度 (%)' },
                margin: { l: 60, r: 20, b: 60, t: 50 },
                showlegend: false
            };

            Plotly.newPlot('ms2Plot', traces, layout, {responsive: true});
        }

        // 生成模拟的碎片离子谱图（高级版）
        function generateFragmentSpectrumAdvanced(precursor) {
            const fragments = [];
            const precursorMz = precursor.precursor_mz;
            const baseIntensity = precursor.intensity;

            // 生成一些典型的碎片离子
            const fragmentTypes = [
                { loss: 18, type: 'H2O丢失', probability: 0.8 },
                { loss: 17, type: 'NH3丢失', probability: 0.6 },
                { loss: 28, type: 'CO丢失', probability: 0.7 },
                { loss: 44, type: 'CO2丢失', probability: 0.5 },
                { loss: 46, type: 'HCOOH丢失', probability: 0.4 },
                { loss: 32, type: 'CH2O丢失', probability: 0.3 },
                { loss: 64, type: 'SO2丢失', probability: 0.2 }
            ];

            fragmentTypes.forEach(frag => {
                if (Math.random() < frag.probability) {
                    const fragMz = precursorMz - frag.loss;
                    if (fragMz > 50) { // 确保碎片离子m/z合理
                        fragments.push({
                            mz: fragMz,
                            intensity: baseIntensity * (0.3 + Math.random() * 0.7),
                            type: frag.type
                        });
                    }
                }
            });

            // 添加一些随机碎片
            for (let i = 0; i < 8 + Math.floor(Math.random() * 15); i++) {
                const fragMz = 50 + Math.random() * (precursorMz - 50);
                fragments.push({
                    mz: fragMz,
                    intensity: baseIntensity * Math.random() * 0.6,
                    type: '其他碎片'
                });
            }

            // 按m/z排序并标准化强度
            fragments.sort((a, b) => a.mz - b.mz);
            const maxIntensity = Math.max(...fragments.map(f => f.intensity));
            fragments.forEach(f => f.intensity = (f.intensity / maxIntensity) * 100);

            return fragments;
        }




    </script>
</body>
</html>
